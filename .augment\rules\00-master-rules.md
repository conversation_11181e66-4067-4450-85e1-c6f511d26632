---
type: "always_apply"
---

# 玄学多语言网站开发 - 主规则文件

## 项目概述

构建一个专业的多语言玄学网站，包含塔罗、星座、星盘等东西方神秘学内容。采用内容驱动的SEO策略，最终发展为权威垂直网站并提供高质量外链服务。

## 核心开发原则

### 1. SEO至上原则

- 每个页面必须包含完整的SEO配置
- 使用Next.js的generateMetadata进行动态SEO
- 实现正确的标题层级结构 (h1 > h2 > h3)
- 包含结构化数据(JSON-LD)以获得富媒体摘要
- 优化核心网络指标(Core Web Vitals)
- 实现完整的站点地图和robots.txt

### 2. 性能优化原则

- 所有图片使用Next.js Image组件，配置适当的sizes属性
- 使用动态导入(dynamic import)分割代码
- 实现适当的缓存策略(ISR + Redis)
- 使用Suspense边界处理加载状态
- 预加载关键资源，延迟加载非关键内容

### 3. 多语言架构原则

- 使用next-intl进行国际化，支持多语言扩展策略
- 实现SEO友好的URL结构：/[locale]/[category]/[slug]
- 正确配置hreflang标签
- 为每种语言提供独立的内容和SEO元数据
- 实现语言回退机制
- 支持RTL（从右到左）语言的布局适配
- 针对不同语言的文字长度进行动态布局调整
- 实现文化敏感的颜色和符号系统

### 4. 主题和视觉原则

- **默认浅色主题**：网站必须默认为白色背景，确保最佳的可读性和用户体验
- **深色模式支持**：提供深色主题切换功能，但不能作为默认主题
- **主题切换**：在Header中提供明显的主题切换按钮
- **主题一致性**：确保所有组件在两种主题下都有良好的视觉效果
- **渐进增强**：浅色主题为基础，深色主题为增强功能

### 5. 移动端优先原则

- 采用移动优先的响应式设计策略
- 确保所有触摸目标不小于44px
- 实现流畅的触摸交互和手势支持
- 优化移动端加载性能和用户体验
- 提供移动端专用的导航和布局组件
- 实现适合移动端的内容展示方式
- 确保移动端的可访问性和易用性

### 6. 代码质量原则

- 严格的TypeScript配置，启用所有严格检查
- 使用ESLint + Prettier保持代码风格一致
- 实现完整的错误边界和错误处理
- 遵循组件单一职责原则
- 使用自定义Hooks抽象业务逻辑

## 技术栈架构

### 前端框架

- **Next.js 14 (App Router)** - SSR/SSG混合模式，SEO友好
- **TypeScript** - 严格类型检查，提升代码质量
- **Tailwind CSS** - 响应式设计，快速样式开发
- **Framer Motion** - 优雅的页面动画和过渡效果
- **React Hook Form** - 表单处理和验证
- **Zustand** - 轻量级状态管理

### 后端技术

- **Next.js API Routes** - 服务端逻辑处理
- **Supabase PostgreSQL** - 主数据库，免费500MB存储，内置认证和实时功能
- **Prisma ORM** - 数据库操作和类型安全
- **Upstash Redis** - 免费缓存服务，10K请求/天，256MB存储
- **Neon PostgreSQL** - 备用数据库，开发环境，免费512MB
- **CloudFlare** - CDN和安全防护

### AI集成技术

- **通义千问 (Qwen)** - 阿里云大语言模型，主要AI服务提供商
- **豆包 (Doubao)** - 字节跳动AI模型，备用服务
- **智谱AI (ZhipuAI)** - 国产AI模型，多语言支持
- **AI服务管理** - 智能路由、负载均衡、故障转移
- **提示词工程** - 专业的玄学领域提示词库

### 内容管理

- **PostgreSQL数据库** - 统一的内容存储，支持富文本和多语言
- **数据库直接管理** - 简化的内容创建和编辑流程
- **Sharp** - 图片优化和处理
- **AI内容生成** - 直接生成并存储到数据库

### 部署和监控

- **Vercel** - 部署平台，边缘计算优化
- **Sentry** - 错误监控和性能分析
- **Umami** - 开源Web分析，隐私友好
- **Uptime Robot** - 网站监控

## 多语言扩展策略

### 第一阶段：核心市场语言 (立即实施)

```typescript
const PHASE_1_LANGUAGES = {
  en: 'English',     // 全球通用语，最大市场
  'zh-CN': '简体中文', // 中国大陆市场 (14亿人口)
  'zh-TW': '繁體中文', // 台湾+香港+海外华人市场 (2500万人口)
  es: 'Español',     // 西班牙+拉美市场 (5亿人口)
  pt: 'Português',   // 巴西+葡语区 (2.6亿人口)
  hi: 'हिन्दी',       // 印度北部核心市场 (6亿人口)
  ja: '日本語',       // 日本高消费市场 (1.25亿人口)
};
```

### 第二阶段：重要区域语言 (6个月后)

```typescript
const PHASE_2_LANGUAGES = {
  de: 'Deutsch',     // 德国+德语区 (1亿人口)
  fr: 'Français',    // 法国+法语区 (2.8亿人口)
  it: 'Italiano',    // 意大利传统占星市场 (6500万人口)
  ru: 'Русский',     // 俄罗斯+东欧 (2.6亿人口)
  ko: '한국어',       // 韩国新兴市场 (5100万人口)
  ar: 'العربية',     // 阿拉伯世界 (4.2亿人口)
};
```

## 项目结构设计

### 简化架构（纯数据库存储）

```
mystical-website/
├── README.md
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── package.json
├── prisma/
│   ├── schema.prisma
│   ├── migrations/
│   └── seed.ts
├── public/
│   ├── images/
│   ├── icons/
│   └── sitemap.xml
├── src/
│   ├── app/
│   │   ├── [locale]/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx                 # 落地页（测试导航中心）
│   │   │   ├── blog/                    # 博客模块
│   │   │   │   ├── page.tsx             # 博客列表
│   │   │   │   ├── [category]/          # 博客分类
│   │   │   │   └── [slug]/              # 博客详情
│   │   │   ├── tarot/                   # 塔罗专题（内容+测试）
│   │   │   │   ├── page.tsx             # 塔罗首页（介绍+测试入口）
│   │   │   │   ├── test/                # 塔罗测试功能
│   │   │   │   │   ├── page.tsx         # 测试页面
│   │   │   │   │   ├── result/[id]/     # 测试结果页
│   │   │   │   │   └── share/[token]/   # 分享结果页
│   │   │   │   ├── guide/               # 塔罗指南内容
│   │   │   │   ├── cards/               # 塔罗牌介绍
│   │   │   │   └── history/             # 塔罗历史
│   │   │   ├── astrology/               # 星座专题（内容+测试）
│   │   │   │   ├── page.tsx             # 星座首页
│   │   │   │   ├── test/                # 星座测试功能
│   │   │   │   ├── signs/               # 12星座详解
│   │   │   │   ├── compatibility/       # 星座配对
│   │   │   │   └── horoscope/           # 星座运势
│   │   │   ├── numerology/              # 数字命理（内容+测试）
│   │   │   │   ├── page.tsx             # 数字命理首页
│   │   │   │   ├── test/                # 数字命理测试
│   │   │   │   ├── calculator/          # 生命数字计算器
│   │   │   │   └── meanings/            # 数字含义解读
│   │   │   ├── crystal/                 # 水晶能量（内容+测试）
│   │   │   │   ├── page.tsx             # 水晶首页
│   │   │   │   ├── test/                # 水晶测试
│   │   │   │   ├── types/               # 水晶种类
│   │   │   │   └── healing/             # 水晶疗愈
│   │   │   ├── palmistry/               # 手相学（内容+测试）
│   │   │   │   ├── page.tsx             # 手相首页
│   │   │   │   ├── test/                # 手相测试
│   │   │   │   ├── lines/               # 手相线条解读
│   │   │   │   └── shapes/              # 手型分析
│   │   │   ├── dreams/                  # 梦境解析（内容+测试）
│   │   │   │   ├── page.tsx             # 梦境首页
│   │   │   │   ├── test/                # 梦境测试
│   │   │   │   ├── symbols/             # 梦境符号
│   │   │   │   └── interpretation/      # 梦境解读
│   │   │   └── admin/                   # 管理后台
│   │   ├── api/                         # API路由
│   │   │   ├── tests/                   # 测试相关API
│   │   │   ├── ai/                      # AI服务API
│   │   │   └── blog/                    # 博客API
│   │   └── middleware.ts
│   ├── components/
│   │   ├── ui/                          # 基础UI组件
│   │   ├── layout/                      # 布局组件
│   │   ├── seo/                         # SEO组件
│   │   ├── blog/                        # 博客组件
│   │   ├── mystical/                    # 玄学专用组件
│   │   │   ├── tarot/                   # 塔罗组件
│   │   │   ├── astrology/               # 星座组件
│   │   │   ├── numerology/              # 数字命理组件
│   │   │   ├── crystal/                 # 水晶组件
│   │   │   ├── palmistry/               # 手相组件
│   │   │   └── dreams/                  # 梦境组件
│   │   ├── tests/                       # 通用测试组件
│   │   │   ├── TestContainer.tsx        # 测试容器
│   │   │   ├── QuestionCard.tsx         # 问题卡片
│   │   │   ├── ProgressBar.tsx          # 进度条
│   │   │   ├── ResultDisplay.tsx        # 结果展示
│   │   │   └── ShareResult.tsx          # 结果分享
│   │   └── ai/                          # AI集成组件
│   ├── lib/                             # 工具库
│   ├── hooks/                           # 自定义Hooks
│   ├── stores/                          # 状态管理
│   ├── types/                           # TypeScript类型
│   ├── styles/                          # 样式文件
│   └── data/                            # 静态数据
├── messages/                            # 国际化文件
│   ├── en.json                          # 英语（全球通用）
│   ├── zh-CN.json                       # 简体中文（中国大陆市场）
│   ├── zh-TW.json                       # 繁体中文（台湾+香港+海外华人）
│   ├── es.json                          # 西班牙语（西班牙+拉美）
│   ├── pt.json                          # 葡萄牙语（巴西+葡语区）
│   ├── hi.json                          # 印地语（印度核心市场）
│   ├── ja.json                          # 日语（日本高消费市场）
│   ├── de.json                          # 德语（德国+德语区）
│   ├── fr.json                          # 法语（法国+法语区）
│   ├── it.json                          # 意大利语（传统占星市场）
│   ├── ru.json                          # 俄语（俄罗斯+东欧）
│   ├── ko.json                          # 韩语（韩国新兴市场）
│   └── ar.json                          # 阿拉伯语（阿拉伯世界）
└── docs/                               # 项目文档
    ├── api.md
    ├── deployment.md
    └── database-management.md
```

### 简化架构说明

#### 1. 纯数据库存储优势
- **单一数据源**：所有内容存储在PostgreSQL数据库中，避免多重存储同步问题
- **AI友好**：AI生成的内容可以直接存储到数据库，无需文件操作
- **动态内容**：支持实时更新，无需重新构建
- **简化管理**：通过管理后台直接操作数据库，流程简单直接

#### 2. 内容+测试一体化设计
- **每个玄学专题都包含测试功能**：`/tarot/test/`、`/astrology/test/` 等
- **SEO友好的URL结构**：测试页面继承专题页面的SEO权重
- **用户体验一致性**：从内容到测试的无缝转换

#### 3. 落地页策略设计
```typescript
// 落地页内容策略
const LANDING_PAGE_STRATEGY = {
  // 主落地页 (/)
  homePage: {
    purpose: '测试导航中心 + 品牌展示',
    structure: {
      hero: {
        title: '探索你的神秘密码',
        subtitle: '专业AI分析，准确解读你的性格与命运',
        cta: '开始免费测试'
      },

      featuredTests: {
        layout: '3x2网格布局',
        tests: ['塔罗牌测试', '星座性格测试', '数字命理测试', '水晶能量测试', '手相解读', '梦境解析'],
        display: '测试卡片 + 简介 + 立即测试按钮'
      },

      socialProof: {
        stats: '已有X万人完成测试',
        testimonials: '用户评价轮播',
        recentActivity: '最近测试动态'
      },

      contentPreview: {
        blogHighlights: '精选博客文章',
        knowledgeBase: '玄学知识库入口'
      }
    },

    seo: {
      title: '免费玄学测试 - 塔罗牌、星座、数字命理AI智能分析',
      description: '专业的在线玄学测试平台，提供塔罗牌、星座性格、数字命理等免费测试，AI智能分析，准确解读性格特质和人生指导。',
      keywords: ['玄学测试', '免费测试', 'AI分析', '塔罗牌', '星座', '数字命理']
    }
  },

  // 专题落地页 (/tarot/, /astrology/ 等)
  categoryPages: {
    purpose: '专题介绍 + 测试入口 + 相关内容',
    structure: {
      hero: {
        title: '{专题名称}完全指南',
        subtitle: '深入了解{专题}的奥秘，获得专业AI分析',
        cta: '立即开始{专题}测试'
      },

      testSection: {
        prominentCTA: '免费{专题}测试',
        testPreview: '测试样例展示',
        benefits: '测试价值说明'
      },

      contentSections: {
        introduction: '专题介绍',
        guide: '入门指南',
        advanced: '进阶内容',
        relatedTests: '相关测试推荐'
      }
    }
  }
};
```

#### 4. URL结构优化
```typescript
// SEO友好的URL结构
const URL_STRUCTURE = {
  // 主要页面
  home: '/',
  blog: '/blog',

  // 专题页面（内容+测试一体化）
  tarot: {
    home: '/tarot',                    # 塔罗首页
    test: '/tarot/test',               # 塔罗测试
    result: '/tarot/test/result/[id]', # 测试结果
    share: '/tarot/test/share/[token]',# 分享结果
    guide: '/tarot/guide',             # 塔罗指南
    cards: '/tarot/cards/[cardName]',  # 单张牌介绍
    history: '/tarot/history'          # 塔罗历史
  },

  astrology: {
    home: '/astrology',
    test: '/astrology/test',
    result: '/astrology/test/result/[id]',
    share: '/astrology/test/share/[token]',
    signs: '/astrology/signs/[sign]',
    compatibility: '/astrology/compatibility',
    horoscope: '/astrology/horoscope'
  },

  // 其他专题类似结构...
};
```

## 商业化策略

### 收入模式设计

1. **外链服务（终极目标）** - 客座文章发布、高质量外链、内容营销服务

### SEO权威性建设路径

- **第一阶段**：内容建设 (0-6个月) - 500+优质文章，10万+月访问量，DA 20+
- **第二阶段**：权威确立 (6-18个月) - 1000+文章，50万+月访问量，DA 40+
- **第三阶段**：商业变现 (18个月+) - DA 60+，100万+月访问量，外链服务

## 专门规则文件引用

当需要开发特定功能模块时，请引用对应的专门规则文件：

- **前端设计系统**: `.augment/rules/01-frontend-design-rules.md` - 基础设计语言、颜色系统、字体、动画、通用组件规范
- **组件架构设计**: `.augment/rules/02-component-architecture-rules.md` - 组件开发模式、状态管理、性能优化
- **博文管理系统**: `.augment/rules/03-blog-management-rules.md` - 内容管理、SEO优化、文章结构
- **在线测试功能**: `.augment/rules/04-online-tests-rules.md` - 测试系统、AI集成、用户交互
- **部署和监控**: `.augment/rules/05-deployment-monitoring-rules.md` - 部署流程、性能监控、错误处理
- **移动端和多语言**: `.augment/rules/06-mobile-multilingual-rules.md` - 移动端适配、多语言布局、跨文化设计

### 规则文件协作关系
- **01和06协作**：01提供基础设计系统，06专注移动端和多语言的具体实现
- **02和其他文件**：02提供组件架构基础，其他文件定义具体业务组件
- **所有文件**：都应遵循00主规则文件中定义的核心原则和技术栈

## 开发工作流

### 代码质量控制

- 使用Husky实现Git Hooks
- pre-commit代码格式化和静态检查
- 代码审查机制
- 自动化测试集成

### 版本控制策略

- 功能分支开发模式
- 语义化版本控制
- 变更日志维护
- 回滚策略预案

## 简化的开发工作流

### 内容创建流程
1. **AI生成内容** → 使用AI服务生成博文内容
2. **直接入库** → 内容直接存储到PostgreSQL数据库
3. **自动优化** → 系统自动处理SEO、slug、阅读时间等
4. **即时发布** → 可选择立即发布或保存为草稿
5. **ISR缓存** → Next.js自动生成静态页面并缓存

### 内容管理流程
1. **管理后台** → 通过Web界面管理所有内容
2. **实时编辑** → 直接在数据库中编辑内容
3. **版本控制** → 通过updatedAt字段跟踪变更
4. **批量操作** → 支持批量发布、删除、更新

### 部署流程
1. **代码推送** → Git push到GitHub
2. **自动部署** → Vercel自动构建和部署
3. **数据库迁移** → Prisma自动处理数据库变更
4. **缓存清理** → 自动清理相关页面缓存

记住：这个项目的核心目标是通过高质量内容建立SEO权威性，最终发展为可持续的外链服务业务。简化的技术架构让我们能专注于内容质量而不是复杂的技术实现。